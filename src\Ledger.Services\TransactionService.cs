using Ledger.Db.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Ledger.Services;

/// <summary>
/// 交易流水服务实现
/// </summary>
public class TransactionService : ITransactionService
{
    private readonly LedgerContext _context;
    private readonly ILogger<TransactionService> _logger;

    public TransactionService(LedgerContext context, ILogger<TransactionService> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<List<Transaction>> GetMonthlyTransactionsAsync(int userId, string yearMonth)
    {
        try
        {
            _logger.LogInformation($"Querying transactions for user {userId} in {yearMonth}");

            // 使用超时控制
            using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(30));

            var transactions = await _context.Transactions
                .Include(t => t.Category)
                .Where(t => t.UserId == userId &&
                           t.YearMonth == yearMonth &&
                           !t.IsDeleted)
                .OrderByDescending(t => t.TransactionDate)
                .ThenByDescending(t => t.CreatedAt)
                .ToListAsync(cts.Token);

            _logger.LogInformation($"Retrieved {transactions.Count} transactions for user {userId} in {yearMonth}");
            return transactions;
        }
        catch (OperationCanceledException)
        {
            _logger.LogWarning($"Query timeout for transactions user {userId} in {yearMonth}");
            // 返回空列表而不是抛出异常
            return new List<Transaction>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error retrieving transactions for user {userId} in {yearMonth}");
            // 返回空列表而不是抛出异常，让界面能正常显示
            return new List<Transaction>();
        }
    }

    public async Task<Transaction> AddTransactionAsync(Transaction transaction)
    {
        try
        {
            // 设置年月字段
            transaction.YearMonth = transaction.TransactionDate.ToString("yyyy-MM");
            transaction.CreatedAt = DateTime.UtcNow;
            transaction.UpdatedAt = DateTime.UtcNow;

            _context.Transactions.Add(transaction);
            await _context.SaveChangesAsync();

            // 重新加载以获取关联的分类信息
            await _context.Entry(transaction)
                .Reference(t => t.Category)
                .LoadAsync();

            _logger.LogInformation($"Added new transaction with ID {transaction.Id}");
            return transaction;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding new transaction");
            throw;
        }
    }

    public async Task<Transaction> UpdateTransactionAsync(Transaction transaction)
    {
        try
        {
            transaction.YearMonth = transaction.TransactionDate.ToString("yyyy-MM");
            transaction.UpdatedAt = DateTime.UtcNow;

            _context.Transactions.Update(transaction);
            await _context.SaveChangesAsync();

            _logger.LogInformation($"Updated transaction with ID {transaction.Id}");
            return transaction;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error updating transaction with ID {transaction.Id}");
            throw;
        }
    }

    public async Task<bool> DeleteTransactionAsync(int transactionId)
    {
        try
        {
            var transaction = await _context.Transactions.FindAsync(transactionId);
            if (transaction == null)
            {
                _logger.LogWarning($"Transaction with ID {transactionId} not found");
                return false;
            }

            transaction.IsDeleted = true;
            transaction.UpdatedAt = DateTime.UtcNow;
            await _context.SaveChangesAsync();

            _logger.LogInformation($"Soft deleted transaction with ID {transactionId}");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error deleting transaction with ID {transactionId}");
            throw;
        }
    }

    public async Task<MonthlyStatistics> GetMonthlyStatisticsAsync(int userId, string yearMonth)
    {
        try
        {
            _logger.LogInformation($"Generating statistics for user {userId} in {yearMonth}");

            // 使用超时控制
            using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(30));

            var transactions = await _context.Transactions
                .Where(t => t.UserId == userId &&
                           t.YearMonth == yearMonth &&
                           !t.IsDeleted)
                .ToListAsync(cts.Token);

            var statistics = new MonthlyStatistics
            {
                YearMonth = yearMonth,
                TotalIncome = transactions.Where(t => t.Type == "income").Sum(t => t.Amount),
                TotalExpense = transactions.Where(t => t.Type == "expense").Sum(t => t.Amount),
                TransactionCount = transactions.Count,
                IncomeCount = transactions.Count(t => t.Type == "income"),
                ExpenseCount = transactions.Count(t => t.Type == "expense")
            };

            _logger.LogInformation($"Generated statistics for user {userId} in {yearMonth}: {statistics.TransactionCount} transactions");
            return statistics;
        }
        catch (OperationCanceledException)
        {
            _logger.LogWarning($"Query timeout for statistics user {userId} in {yearMonth}");
            // 返回空统计而不是抛出异常
            return new MonthlyStatistics { YearMonth = yearMonth };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error generating statistics for user {userId} in {yearMonth}");
            // 返回空统计而不是抛出异常
            return new MonthlyStatistics { YearMonth = yearMonth };
        }
    }

    public async Task<List<MonthlyStatistics>> GetYearlyStatisticsAsync(int userId, int year)
    {
        try
        {
            var yearPrefix = year.ToString();
            var transactions = await _context.Transactions
                .Where(t => t.UserId == userId &&
                           t.YearMonth.StartsWith(yearPrefix) &&
                           !t.IsDeleted)
                .ToListAsync();

            var monthlyStats = transactions
                .GroupBy(t => t.YearMonth)
                .Select(g => new MonthlyStatistics
                {
                    YearMonth = g.Key,
                    TotalIncome = g.Where(t => t.Type == "income").Sum(t => t.Amount),
                    TotalExpense = g.Where(t => t.Type == "expense").Sum(t => t.Amount),
                    TransactionCount = g.Count(),
                    IncomeCount = g.Count(t => t.Type == "income"),
                    ExpenseCount = g.Count(t => t.Type == "expense")
                })
                .OrderBy(s => s.YearMonth)
                .ToList();

            _logger.LogInformation($"Generated yearly statistics for user {userId} in {year}");
            return monthlyStats;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error generating yearly statistics for user {userId} in {year}");
            throw;
        }
    }
}
