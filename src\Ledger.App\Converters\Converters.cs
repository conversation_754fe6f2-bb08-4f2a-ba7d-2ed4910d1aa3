using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace Ledger.App;

/// <summary>
/// 转换器集合
/// </summary>
public static class Converters
{
    /// <summary>
    /// 数量到可见性转换器（0时隐藏，大于0时显示）
    /// </summary>
    public static readonly IValueConverter CountToVisibilityConverter = new CountToVisibilityConverterImpl();

    /// <summary>
    /// DateOnly到DateTime转换器
    /// </summary>
    public static readonly IValueConverter DateOnlyToDateTimeConverter = new DateOnlyToDateTimeConverterImpl();

    /// <summary>
    /// 布尔值到可见性转换器（反向）
    /// </summary>
    public static readonly IValueConverter InverseBooleanToVisibilityConverter = new InverseBooleanToVisibilityConverterImpl();
}

/// <summary>
/// 数量到可见性转换器实现
/// </summary>
internal class CountToVisibilityConverterImpl : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is int count)
        {
            return count == 0 ? Visibility.Visible : Visibility.Collapsed;
        }
        return Visibility.Collapsed;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

/// <summary>
/// DateOnly到DateTime转换器实现
/// </summary>
internal class DateOnlyToDateTimeConverterImpl : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is DateOnly dateOnly)
        {
            return dateOnly.ToDateTime(TimeOnly.MinValue);
        }
        return DateTime.Today;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is DateTime dateTime)
        {
            return DateOnly.FromDateTime(dateTime);
        }
        return DateOnly.FromDateTime(DateTime.Today);
    }
}

/// <summary>
/// 布尔值到可见性转换器实现（反向）
/// </summary>
internal class InverseBooleanToVisibilityConverterImpl : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is bool boolValue)
        {
            return boolValue ? Visibility.Collapsed : Visibility.Visible;
        }
        return Visibility.Visible;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is Visibility visibility)
        {
            return visibility != Visibility.Visible;
        }
        return false;
    }
}
