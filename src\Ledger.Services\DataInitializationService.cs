using Ledger.Db.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Ledger.Services;

/// <summary>
/// 数据初始化服务
/// </summary>
public class DataInitializationService
{
    private readonly LedgerContext _context;
    private readonly ILogger<DataInitializationService> _logger;

    public DataInitializationService(LedgerContext context, ILogger<DataInitializationService> logger)
    {
        _context = context;
        _logger = logger;
    }

    /// <summary>
    /// 初始化默认数据
    /// </summary>
    public async Task InitializeDefaultDataAsync()
    {
        try
        {
            // 确保数据库已创建
            await _context.Database.EnsureCreatedAsync();

            // 初始化默认分类
            await InitializeDefaultCategoriesAsync();

            // 初始化示例交易数据
            await InitializeSampleTransactionsAsync();

            _logger.LogInformation("Default data initialization completed");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during data initialization");
            throw;
        }
    }

    /// <summary>
    /// 初始化默认分类
    /// </summary>
    private async Task InitializeDefaultCategoriesAsync()
    {
        try
        {
            // 检查是否已有分类数据
            var existingCategoriesCount = await _context.Categories.CountAsync();
            if (existingCategoriesCount > 0)
            {
                _logger.LogInformation("Categories already exist, skipping initialization");
                return;
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to count existing categories, proceeding with initialization");
        }

        var defaultCategories = new List<Category>
        {
            // 支出分类
            new Category { Name = "餐饮", Type = "expense", Icon = "🍽️", IsDefault = true, SortOrder = 1 },
            new Category { Name = "交通", Type = "expense", Icon = "🚗", IsDefault = true, SortOrder = 2 },
            new Category { Name = "购物", Type = "expense", Icon = "🛒", IsDefault = true, SortOrder = 3 },
            new Category { Name = "娱乐", Type = "expense", Icon = "🎮", IsDefault = true, SortOrder = 4 },
            new Category { Name = "医疗", Type = "expense", Icon = "🏥", IsDefault = true, SortOrder = 5 },
            new Category { Name = "教育", Type = "expense", Icon = "📚", IsDefault = true, SortOrder = 6 },
            new Category { Name = "住房", Type = "expense", Icon = "🏠", IsDefault = true, SortOrder = 7 },
            new Category { Name = "通讯", Type = "expense", Icon = "📱", IsDefault = true, SortOrder = 8 },
            new Category { Name = "服装", Type = "expense", Icon = "👕", IsDefault = true, SortOrder = 9 },
            new Category { Name = "其他支出", Type = "expense", Icon = "💸", IsDefault = true, SortOrder = 10 },

            // 收入分类
            new Category { Name = "工资", Type = "income", Icon = "💰", IsDefault = true, SortOrder = 1 },
            new Category { Name = "奖金", Type = "income", Icon = "🎁", IsDefault = true, SortOrder = 2 },
            new Category { Name = "投资收益", Type = "income", Icon = "📈", IsDefault = true, SortOrder = 3 },
            new Category { Name = "兼职收入", Type = "income", Icon = "💼", IsDefault = true, SortOrder = 4 },
            new Category { Name = "红包", Type = "income", Icon = "🧧", IsDefault = true, SortOrder = 5 },
            new Category { Name = "退款", Type = "income", Icon = "↩️", IsDefault = true, SortOrder = 6 },
            new Category { Name = "其他收入", Type = "income", Icon = "💵", IsDefault = true, SortOrder = 7 }
        };

        foreach (var category in defaultCategories)
        {
            category.CreatedAt = DateTime.UtcNow;
            category.UpdatedAt = DateTime.UtcNow;
        }

        _context.Categories.AddRange(defaultCategories);

        try
        {
            await _context.SaveChangesAsync();
            _logger.LogInformation($"Initialized {defaultCategories.Count} default categories");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to save default categories");
            throw;
        }
    }
}
