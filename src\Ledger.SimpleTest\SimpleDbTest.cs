using Ledger.Db.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;

namespace Ledger.SimpleTest;

public class SimpleDbTest
{
    public static async Task TestConnection()
    {
        Console.WriteLine("=== 简单数据库连接测试 ===");
        
        try
        {
            // 读取配置
            var configuration = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json")
                .Build();
            
            var connectionString = configuration.GetConnectionString("DefaultConnection");
            Console.WriteLine($"连接字符串: {connectionString?.Substring(0, Math.Min(50, connectionString.Length))}...");
            
            // 创建数据库上下文
            var options = new DbContextOptionsBuilder<LedgerContext>()
                .UseNpgsql(connectionString)
                .Options;
            
            using var context = new LedgerContext(options);
            
            // 测试连接
            Console.WriteLine("正在测试数据库连接...");
            var canConnect = await context.Database.CanConnectAsync();
            Console.WriteLine($"数据库连接测试: {(canConnect ? "✓ 成功" : "❌ 失败")}");
            
            if (canConnect)
            {
                // 确保数据库已创建
                Console.WriteLine("正在确保数据库已创建...");
                await context.Database.EnsureCreatedAsync();
                Console.WriteLine("✓ 数据库已创建");
                
                // 检查分类表
                Console.WriteLine("正在检查分类数据...");
                var categoryCount = await context.Categories.CountAsync();
                Console.WriteLine($"✓ 分类数量: {categoryCount}");
                
                if (categoryCount == 0)
                {
                    Console.WriteLine("正在添加测试分类...");
                    var testCategory = new Category
                    {
                        Name = "测试分类",
                        Type = "expense",
                        Icon = "🧪",
                        IsDefault = false,
                        SortOrder = 999,
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow
                    };
                    
                    context.Categories.Add(testCategory);
                    await context.SaveChangesAsync();
                    Console.WriteLine("✓ 测试分类已添加");
                }
                
                // 显示前几个分类
                var categories = await context.Categories.Take(5).ToListAsync();
                Console.WriteLine("前5个分类:");
                foreach (var category in categories)
                {
                    Console.WriteLine($"  {category.Icon} {category.Name} ({category.Type})");
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 测试失败: {ex.Message}");
            Console.WriteLine($"详细错误: {ex}");
        }
        
        Console.WriteLine("\n测试完成，按任意键退出...");
        Console.ReadKey();
    }
}
