﻿using System.Windows;
using Ledger.Services;
using Ledger.App.ViewModels;
using Ledger.Db.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace Ledger.App
{
    /// <summary>
    /// Interaction logic for App.xaml
    /// </summary>
    public partial class App : Application
    {
        private IHost? _host;

        protected override async void OnStartup(StartupEventArgs e)
        {
            // 创建主机
            _host = CreateHostBuilder().Build();

            // 启动主机
            await _host.StartAsync();

            // 初始化数据
            var dataInitService = _host.Services.GetRequiredService<DataInitializationService>();
            await dataInitService.InitializeDefaultDataAsync();

            // 创建主窗口
            var mainWindow = _host.Services.GetRequiredService<MainWindow>();
            mainWindow.Show();

            base.OnStartup(e);
        }

        protected override async void OnExit(ExitEventArgs e)
        {
            if (_host != null)
            {
                await _host.StopAsync();
                _host.Dispose();
            }

            base.OnExit(e);
        }

        private static IHostBuilder CreateHostBuilder()
        {
            return Host.CreateDefaultBuilder()
                .ConfigureAppConfiguration((context, config) =>
                {
                    config.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);
                })
                .ConfigureServices((context, services) =>
                {
                    // 配置数据库上下文
                    var connectionString = context.Configuration.GetConnectionString("DefaultConnection");
                    services.AddDbContext<LedgerContext>(options =>
                        options.UseNpgsql(connectionString));

                    // 注册服务
                    services.AddScoped<ITransactionService, TransactionService>();
                    services.AddScoped<ICategoryService, CategoryService>();
                    services.AddScoped<DataInitializationService>();

                    // 注册视图模型
                    services.AddTransient<MainViewModel>();

                    // 注册窗口
                    services.AddTransient<MainWindow>();

                    // 配置日志
                    services.AddLogging(builder =>
                    {
                        builder.AddConsole();
                        builder.AddDebug();
                    });
                });
        }
    }
}
