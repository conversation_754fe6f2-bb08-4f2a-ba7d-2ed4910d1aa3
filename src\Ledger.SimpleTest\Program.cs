using Ledger.Db.Models;
using Ledger.SimpleTest;

namespace Ledger.SimpleTest;

class Program
{
    static async Task Main(string[] args)
    {
        Console.WriteLine("=== 个人记账本简单测试 ===");

        try
        {
            // 测试数据库连接
            await SimpleDbTest.TestConnection();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 测试失败: {ex.Message}");
            Console.WriteLine($"详细错误: {ex}");
        }
    }

    static void TestModels()
    {
        Console.WriteLine("\n--- 测试数据模型 ---");

        // 测试用户模型
        var user = new User
        {
            Id = 1,
            Username = "testuser",
            Nickname = "测试用户",
            Email = "<EMAIL>",
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
            Disabled = false
        };
        Console.WriteLine($"✓ 用户模型: {user.Nickname} ({user.Username})");

        // 测试分类模型
        var category = new Category
        {
            Id = 1,
            Name = "餐饮",
            Type = "expense",
            Icon = "🍽️",
            IsDefault = true,
            SortOrder = 1,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };
        Console.WriteLine($"✓ 分类模型: {category.Icon} {category.Name} ({category.Type})");

        // 测试交易模型
        var transaction = new Transaction
        {
            Id = 1,
            Amount = 50.00m,
            Type = "expense",
            Description = "午餐",
            TransactionDate = DateOnly.FromDateTime(DateTime.Today),
            CategoryId = category.Id,
            Category = category,
            UserId = user.Id,
            User = user,
            IsDeleted = false,
            YearMonth = DateTime.Today.ToString("yyyy-MM"),
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };
        Console.WriteLine($"✓ 交易模型: {transaction.TransactionDate:yyyy-MM-dd} {transaction.Type} ¥{transaction.Amount:F2} - {transaction.Description}");

        // 测试计算属性
        Console.WriteLine($"✓ 年月: {transaction.YearMonth}");
        Console.WriteLine($"✓ 分类信息: {transaction.Category.Icon} {transaction.Category.Name}");
        Console.WriteLine($"✓ 用户信息: {transaction.User.Nickname}");

        // 测试集合关系
        user.Transactions.Add(transaction);
        category.Transactions.Add(transaction);

        Console.WriteLine($"✓ 用户交易数: {user.Transactions.Count}");
        Console.WriteLine($"✓ 分类交易数: {category.Transactions.Count}");

        // 测试一些业务逻辑
        var isIncome = transaction.Type == "income";
        var isExpense = transaction.Type == "expense";
        var formattedAmount = $"{(isIncome ? "+" : "-")}¥{transaction.Amount:F2}";

        Console.WriteLine($"✓ 是否收入: {isIncome}");
        Console.WriteLine($"✓ 是否支出: {isExpense}");
        Console.WriteLine($"✓ 格式化金额: {formattedAmount}");

        // 测试月度统计逻辑
        var transactions = new List<Transaction>
        {
            new Transaction { Amount = 100m, Type = "income", TransactionDate = DateOnly.FromDateTime(DateTime.Today) },
            new Transaction { Amount = 50m, Type = "expense", TransactionDate = DateOnly.FromDateTime(DateTime.Today) },
            new Transaction { Amount = 30m, Type = "expense", TransactionDate = DateOnly.FromDateTime(DateTime.Today) },
        };

        var totalIncome = transactions.Where(t => t.Type == "income").Sum(t => t.Amount);
        var totalExpense = transactions.Where(t => t.Type == "expense").Sum(t => t.Amount);
        var netIncome = totalIncome - totalExpense;
        var transactionCount = transactions.Count;

        Console.WriteLine($"✓ 模拟月度统计:");
        Console.WriteLine($"  总收入: ¥{totalIncome:F2}");
        Console.WriteLine($"  总支出: ¥{totalExpense:F2}");
        Console.WriteLine($"  净收入: ¥{netIncome:F2}");
        Console.WriteLine($"  交易笔数: {transactionCount}");
    }
}
