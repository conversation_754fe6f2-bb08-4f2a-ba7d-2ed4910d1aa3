<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <StartupObject>Ledger.SimpleTest.TestCategories</StartupObject>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\Ledger.Db\Ledger.Db.csproj" />
    <ProjectReference Include="..\Ledger.Services\Ledger.Services.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="9.0.1" />
    <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="9.0.5" />
  </ItemGroup>

  <ItemGroup>
    <None Update="appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
