using Ledger.Db.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Ledger.Services;

/// <summary>
/// 分类服务实现
/// </summary>
public class CategoryService : ICategoryService
{
    private readonly LedgerContext _context;
    private readonly ILogger<CategoryService> _logger;

    public CategoryService(LedgerContext context, ILogger<CategoryService> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<List<Category>> GetAllCategoriesAsync()
    {
        try
        {
            var categories = await _context.Categories
                .OrderBy(c => c.SortOrder)
                .ThenBy(c => c.Name)
                .ToListAsync();

            _logger.LogInformation($"Retrieved {categories.Count} categories");
            return categories;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving all categories");
            throw;
        }
    }

    public async Task<List<Category>> GetCategoriesByTypeAsync(string type)
    {
        try
        {
            var categories = await _context.Categories
                .Where(c => c.Type == type)
                .OrderBy(c => c.SortOrder)
                .ThenBy(c => c.Name)
                .ToListAsync();

            _logger.LogInformation($"Retrieved {categories.Count} categories for type {type}");
            return categories;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error retrieving categories for type {type}");
            throw;
        }
    }

    public async Task<Category?> GetCategoryByIdAsync(int categoryId)
    {
        try
        {
            var category = await _context.Categories.FindAsync(categoryId);

            if (category == null)
            {
                _logger.LogWarning($"Category with ID {categoryId} not found");
            }

            return category;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error retrieving category with ID {categoryId}");
            throw;
        }
    }

    public async Task<Category> AddCategoryAsync(Category category)
    {
        try
        {
            category.CreatedAt = DateTime.UtcNow;
            category.UpdatedAt = DateTime.UtcNow;

            _context.Categories.Add(category);
            await _context.SaveChangesAsync();

            _logger.LogInformation($"Added new category with ID {category.Id}");
            return category;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding new category");
            throw;
        }
    }

    public async Task<Category> UpdateCategoryAsync(Category category)
    {
        try
        {
            category.UpdatedAt = DateTime.UtcNow;

            _context.Categories.Update(category);
            await _context.SaveChangesAsync();

            _logger.LogInformation($"Updated category with ID {category.Id}");
            return category;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error updating category with ID {category.Id}");
            throw;
        }
    }

    public async Task<bool> DeleteCategoryAsync(int categoryId)
    {
        try
        {
            var category = await _context.Categories.FindAsync(categoryId);
            if (category == null)
            {
                _logger.LogWarning($"Category with ID {categoryId} not found");
                return false;
            }

            // 检查是否有关联的交易记录
            var hasTransactions = await _context.Transactions
                .AnyAsync(t => t.CategoryId == categoryId && !t.IsDeleted);

            if (hasTransactions)
            {
                _logger.LogWarning($"Cannot delete category with ID {categoryId} because it has associated transactions");
                return false;
            }

            _context.Categories.Remove(category);
            await _context.SaveChangesAsync();

            _logger.LogInformation($"Deleted category with ID {categoryId}");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error deleting category with ID {categoryId}");
            throw;
        }
    }
}
