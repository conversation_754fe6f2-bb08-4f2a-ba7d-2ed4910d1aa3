using System.Collections.ObjectModel;
using System.Windows.Input;
using Ledger.App.Commands;
using Ledger.Services;
using Ledger.Db.Models;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace Ledger.App.ViewModels;

/// <summary>
/// 添加交易视图模型
/// </summary>
public class AddTransactionViewModel : ViewModelBase
{
    private readonly ITransactionService _transactionService;
    private readonly ICategoryService _categoryService;
    private readonly IConfiguration _configuration;
    private readonly ILogger _logger;

    private decimal _amount;
    private string _type = "expense";
    private string? _description;
    private DateOnly _transactionDate = DateOnly.FromDateTime(DateTime.Today);
    private Category? _selectedCategory;
    private bool _isLoading;
    private bool _isSaving;

    public AddTransactionViewModel(
        ITransactionService transactionService,
        ICategoryService categoryService,
        IConfiguration configuration,
        ILogger logger)
    {
        _transactionService = transactionService;
        _categoryService = categoryService;
        _configuration = configuration;
        _logger = logger;

        // 初始化集合
        Categories = new ObservableCollection<Category>();

        // 初始化命令
        LoadCategoriesCommand = new AsyncRelayCommand(LoadCategoriesAsync);
        SaveTransactionCommand = new AsyncRelayCommand(SaveTransactionAsync, CanSaveTransaction);
        ResetCommand = new RelayCommand(Reset);

        // 不在构造函数中加载分类，避免与 MainViewModel 的初始化产生 DbContext 冲突
        // 分类将在需要时延迟加载
    }

    #region 属性

    /// <summary>
    /// 金额
    /// </summary>
    public decimal Amount
    {
        get => _amount;
        set => SetProperty(ref _amount, value, () => SaveTransactionCommand.RaiseCanExecuteChanged());
    }

    /// <summary>
    /// 交易类型
    /// </summary>
    public string Type
    {
        get => _type;
        set => SetProperty(ref _type, value, OnTypeChanged);
    }

    /// <summary>
    /// 描述
    /// </summary>
    public string? Description
    {
        get => _description;
        set => SetProperty(ref _description, value);
    }

    /// <summary>
    /// 交易日期
    /// </summary>
    public DateOnly TransactionDate
    {
        get => _transactionDate;
        set => SetProperty(ref _transactionDate, value);
    }

    /// <summary>
    /// 选中的分类
    /// </summary>
    public Category? SelectedCategory
    {
        get => _selectedCategory;
        set => SetProperty(ref _selectedCategory, value, () => SaveTransactionCommand.RaiseCanExecuteChanged());
    }

    /// <summary>
    /// 是否正在加载
    /// </summary>
    public bool IsLoading
    {
        get => _isLoading;
        set => SetProperty(ref _isLoading, value);
    }

    /// <summary>
    /// 是否正在保存
    /// </summary>
    public bool IsSaving
    {
        get => _isSaving;
        set => SetProperty(ref _isSaving, value);
    }

    /// <summary>
    /// 分类列表
    /// </summary>
    public ObservableCollection<Category> Categories { get; }

    /// <summary>
    /// 是否为收入
    /// </summary>
    public bool IsIncome
    {
        get => _type == "income";
        set
        {
            if (value)
                Type = "income";
        }
    }

    /// <summary>
    /// 是否为支出
    /// </summary>
    public bool IsExpense
    {
        get => _type == "expense";
        set
        {
            if (value)
                Type = "expense";
        }
    }

    #endregion

    #region 命令

    /// <summary>
    /// 加载分类命令
    /// </summary>
    public AsyncRelayCommand LoadCategoriesCommand { get; }

    /// <summary>
    /// 保存交易命令
    /// </summary>
    public AsyncRelayCommand SaveTransactionCommand { get; }

    /// <summary>
    /// 重置命令
    /// </summary>
    public ICommand ResetCommand { get; }

    #endregion

    #region 事件

    /// <summary>
    /// 交易添加完成事件
    /// </summary>
    public event EventHandler? TransactionAdded;

    #endregion

    #region 方法

    /// <summary>
    /// 加载分类列表
    /// </summary>
    public async Task LoadCategoriesAsync()
    {
        try
        {
            IsLoading = true;

            try
            {
                // 尝试从数据库加载分类
                var categories = await _categoryService.GetCategoriesByTypeAsync(_type);

                Categories.Clear();
                foreach (var category in categories)
                {
                    Categories.Add(category);
                }

                _logger.LogInformation($"Loaded {categories.Count} categories for type {_type} from database");
            }
            catch (Exception dbEx)
            {
                _logger.LogWarning(dbEx, $"Failed to load categories from database for type {_type}, using fallback categories");

                // 使用备用分类数据
                LoadFallbackCategories();
            }

            // 选择第一个分类作为默认值
            if (Categories.Count > 0)
            {
                SelectedCategory = Categories.First();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error loading categories for type {_type}");
            // 即使出错也提供基本分类
            LoadFallbackCategories();
            if (Categories.Count > 0)
            {
                SelectedCategory = Categories.First();
            }
        }
        finally
        {
            IsLoading = false;
        }
    }

    /// <summary>
    /// 加载备用分类数据
    /// </summary>
    private void LoadFallbackCategories()
    {
        Categories.Clear();

        if (_type == "expense")
        {
            // 支出分类
            Categories.Add(new Category { Id = 1, Name = "餐饮", Icon = "🍽️", Type = "expense", SortOrder = 1 });
            Categories.Add(new Category { Id = 2, Name = "购物", Icon = "🛒", Type = "expense", SortOrder = 2 });
            Categories.Add(new Category { Id = 3, Name = "交通", Icon = "🚗", Type = "expense", SortOrder = 3 });
            Categories.Add(new Category { Id = 4, Name = "娱乐", Icon = "🎮", Type = "expense", SortOrder = 4 });
            Categories.Add(new Category { Id = 5, Name = "医疗", Icon = "🏥", Type = "expense", SortOrder = 5 });
            Categories.Add(new Category { Id = 6, Name = "教育", Icon = "📚", Type = "expense", SortOrder = 6 });
            Categories.Add(new Category { Id = 7, Name = "住房", Icon = "🏠", Type = "expense", SortOrder = 7 });
            Categories.Add(new Category { Id = 8, Name = "通讯", Icon = "📱", Type = "expense", SortOrder = 8 });
            Categories.Add(new Category { Id = 9, Name = "其他", Icon = "📦", Type = "expense", SortOrder = 9 });
        }
        else if (_type == "income")
        {
            // 收入分类
            Categories.Add(new Category { Id = 10, Name = "工资", Icon = "💰", Type = "income", SortOrder = 1 });
            Categories.Add(new Category { Id = 11, Name = "奖金", Icon = "🎁", Type = "income", SortOrder = 2 });
            Categories.Add(new Category { Id = 12, Name = "投资", Icon = "📈", Type = "income", SortOrder = 3 });
            Categories.Add(new Category { Id = 13, Name = "兼职", Icon = "💼", Type = "income", SortOrder = 4 });
            Categories.Add(new Category { Id = 14, Name = "其他", Icon = "💵", Type = "income", SortOrder = 5 });
        }

        _logger.LogInformation($"Loaded {Categories.Count} fallback categories for type {_type}");
    }

    /// <summary>
    /// 保存交易
    /// </summary>
    private async Task SaveTransactionAsync()
    {
        if (!CanSaveTransaction())
            return;

        try
        {
            IsSaving = true;

            var transaction = new Transaction
            {
                Amount = _amount,
                Type = _type,
                Description = _description,
                TransactionDate = _transactionDate,
                CategoryId = _selectedCategory!.Id,
                UserId = _configuration.GetValue<int>("Application:DefaultUserId", 1),
                IsDeleted = false
            };

            await _transactionService.AddTransactionAsync(transaction);

            _logger.LogInformation($"Successfully added transaction: {_type} {_amount:C}");

            // 触发事件
            TransactionAdded?.Invoke(this, EventArgs.Empty);

            // 重置表单
            Reset();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving transaction");
            // TODO: 显示错误消息给用户
        }
        finally
        {
            IsSaving = false;
        }
    }

    /// <summary>
    /// 检查是否可以保存交易
    /// </summary>
    private bool CanSaveTransaction()
    {
        return _amount > 0 && _selectedCategory != null && !_isSaving;
    }

    /// <summary>
    /// 重置表单
    /// </summary>
    public void Reset()
    {
        Amount = 0;
        Type = "expense";
        Description = null;
        TransactionDate = DateOnly.FromDateTime(DateTime.Today);
        SelectedCategory = Categories.FirstOrDefault();
    }

    /// <summary>
    /// 交易类型变更时的处理
    /// </summary>
    private async void OnTypeChanged()
    {
        OnPropertyChanged(nameof(IsIncome));
        OnPropertyChanged(nameof(IsExpense));

        // 重新加载对应类型的分类
        await LoadCategoriesAsync();
    }

    #endregion
}
