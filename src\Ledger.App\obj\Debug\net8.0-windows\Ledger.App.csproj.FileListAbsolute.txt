F:\dotnet_workspace\ledger\src\Ledger.App\bin\Debug\net8.0-windows\Ledger.App.exe
F:\dotnet_workspace\ledger\src\Ledger.App\bin\Debug\net8.0-windows\Ledger.App.deps.json
F:\dotnet_workspace\ledger\src\Ledger.App\bin\Debug\net8.0-windows\Ledger.App.runtimeconfig.json
F:\dotnet_workspace\ledger\src\Ledger.App\bin\Debug\net8.0-windows\Ledger.App.dll
F:\dotnet_workspace\ledger\src\Ledger.App\bin\Debug\net8.0-windows\Ledger.App.pdb
F:\dotnet_workspace\ledger\src\Ledger.App\obj\Debug\net8.0-windows\MainWindow.g.cs
F:\dotnet_workspace\ledger\src\Ledger.App\obj\Debug\net8.0-windows\App.g.cs
F:\dotnet_workspace\ledger\src\Ledger.App\obj\Debug\net8.0-windows\Ledger.App_MarkupCompile.cache
F:\dotnet_workspace\ledger\src\Ledger.App\obj\Debug\net8.0-windows\Ledger.App_MarkupCompile.lref
F:\dotnet_workspace\ledger\src\Ledger.App\obj\Debug\net8.0-windows\MainWindow.baml
F:\dotnet_workspace\ledger\src\Ledger.App\obj\Debug\net8.0-windows\Ledger.App.g.resources
F:\dotnet_workspace\ledger\src\Ledger.App\obj\Debug\net8.0-windows\Ledger.App.GeneratedMSBuildEditorConfig.editorconfig
F:\dotnet_workspace\ledger\src\Ledger.App\obj\Debug\net8.0-windows\Ledger.App.AssemblyInfoInputs.cache
F:\dotnet_workspace\ledger\src\Ledger.App\obj\Debug\net8.0-windows\Ledger.App.AssemblyInfo.cs
F:\dotnet_workspace\ledger\src\Ledger.App\obj\Debug\net8.0-windows\Ledger.App.csproj.CoreCompileInputs.cache
F:\dotnet_workspace\ledger\src\Ledger.App\obj\Debug\net8.0-windows\Ledger.App.dll
F:\dotnet_workspace\ledger\src\Ledger.App\obj\Debug\net8.0-windows\refint\Ledger.App.dll
F:\dotnet_workspace\ledger\src\Ledger.App\obj\Debug\net8.0-windows\Ledger.App.pdb
F:\dotnet_workspace\ledger\src\Ledger.App\obj\Debug\net8.0-windows\Ledger.App.genruntimeconfig.cache
F:\dotnet_workspace\ledger\src\Ledger.App\obj\Debug\net8.0-windows\ref\Ledger.App.dll
