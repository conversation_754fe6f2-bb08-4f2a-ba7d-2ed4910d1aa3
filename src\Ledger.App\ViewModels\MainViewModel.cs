using System.Collections.ObjectModel;
using System.Windows.Input;
using Ledger.App.Commands;
using Ledger.App.Services;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace Ledger.App.ViewModels;

/// <summary>
/// 主窗口视图模型
/// </summary>
public class MainViewModel : ViewModelBase
{
    private readonly ITransactionService _transactionService;
    private readonly ICategoryService _categoryService;
    private readonly IConfiguration _configuration;
    private readonly ILogger<MainViewModel> _logger;

    private string _currentYearMonth;
    private int _currentUserId;
    private bool _isLoading;
    private string _selectedTabIndex = "0";

    public MainViewModel(
        ITransactionService transactionService,
        ICategoryService categoryService,
        IConfiguration configuration,
        ILogger<MainViewModel> logger)
    {
        _transactionService = transactionService;
        _categoryService = categoryService;
        _configuration = configuration;
        _logger = logger;

        // 从配置获取默认用户ID
        _currentUserId = _configuration.GetValue<int>("Application:DefaultUserId", 1);
        
        // 设置当前年月为本月
        _currentYearMonth = DateTime.Now.ToString("yyyy-MM");

        // 初始化集合
        Transactions = new ObservableCollection<TransactionItemViewModel>();

        // 初始化命令
        LoadTransactionsCommand = new AsyncRelayCommand(LoadTransactionsAsync);
        RefreshCommand = new AsyncRelayCommand(RefreshAsync);
        PreviousMonthCommand = new RelayCommand(PreviousMonth);
        NextMonthCommand = new RelayCommand(NextMonth);
        ShowAddTransactionCommand = new RelayCommand(ShowAddTransaction);

        // 初始化子视图模型
        AddTransactionViewModel = new AddTransactionViewModel(_transactionService, _categoryService, _logger);
        MonthlyStatisticsViewModel = new MonthlyStatisticsViewModel(_transactionService, _logger);

        // 订阅事件
        AddTransactionViewModel.TransactionAdded += OnTransactionAdded;
    }

    #region 属性

    /// <summary>
    /// 当前年月
    /// </summary>
    public string CurrentYearMonth
    {
        get => _currentYearMonth;
        set => SetProperty(ref _currentYearMonth, value, OnCurrentYearMonthChanged);
    }

    /// <summary>
    /// 当前用户ID
    /// </summary>
    public int CurrentUserId
    {
        get => _currentUserId;
        set => SetProperty(ref _currentUserId, value);
    }

    /// <summary>
    /// 是否正在加载
    /// </summary>
    public bool IsLoading
    {
        get => _isLoading;
        set => SetProperty(ref _isLoading, value);
    }

    /// <summary>
    /// 选中的标签页索引
    /// </summary>
    public string SelectedTabIndex
    {
        get => _selectedTabIndex;
        set => SetProperty(ref _selectedTabIndex, value);
    }

    /// <summary>
    /// 交易列表
    /// </summary>
    public ObservableCollection<TransactionItemViewModel> Transactions { get; }

    /// <summary>
    /// 添加交易视图模型
    /// </summary>
    public AddTransactionViewModel AddTransactionViewModel { get; }

    /// <summary>
    /// 月度统计视图模型
    /// </summary>
    public MonthlyStatisticsViewModel MonthlyStatisticsViewModel { get; }

    /// <summary>
    /// 格式化的当前年月显示
    /// </summary>
    public string FormattedCurrentYearMonth
    {
        get
        {
            if (DateTime.TryParseExact(_currentYearMonth, "yyyy-MM", null, System.Globalization.DateTimeStyles.None, out var date))
            {
                return date.ToString("yyyy年MM月");
            }
            return _currentYearMonth;
        }
    }

    #endregion

    #region 命令

    /// <summary>
    /// 加载交易列表命令
    /// </summary>
    public ICommand LoadTransactionsCommand { get; }

    /// <summary>
    /// 刷新命令
    /// </summary>
    public ICommand RefreshCommand { get; }

    /// <summary>
    /// 上一月命令
    /// </summary>
    public ICommand PreviousMonthCommand { get; }

    /// <summary>
    /// 下一月命令
    /// </summary>
    public ICommand NextMonthCommand { get; }

    /// <summary>
    /// 显示添加交易命令
    /// </summary>
    public ICommand ShowAddTransactionCommand { get; }

    #endregion

    #region 方法

    /// <summary>
    /// 初始化
    /// </summary>
    public async Task InitializeAsync()
    {
        await LoadTransactionsAsync();
        await MonthlyStatisticsViewModel.LoadStatisticsAsync(_currentUserId, _currentYearMonth);
    }

    /// <summary>
    /// 加载交易列表
    /// </summary>
    private async Task LoadTransactionsAsync()
    {
        try
        {
            IsLoading = true;
            
            var transactions = await _transactionService.GetMonthlyTransactionsAsync(_currentUserId, _currentYearMonth);
            
            Transactions.Clear();
            foreach (var transaction in transactions)
            {
                Transactions.Add(new TransactionItemViewModel(transaction));
            }

            _logger.LogInformation($"Loaded {transactions.Count} transactions for {_currentYearMonth}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading transactions");
            // TODO: 显示错误消息给用户
        }
        finally
        {
            IsLoading = false;
        }
    }

    /// <summary>
    /// 刷新数据
    /// </summary>
    private async Task RefreshAsync()
    {
        await LoadTransactionsAsync();
        await MonthlyStatisticsViewModel.LoadStatisticsAsync(_currentUserId, _currentYearMonth);
    }

    /// <summary>
    /// 上一月
    /// </summary>
    private void PreviousMonth()
    {
        if (DateTime.TryParseExact(_currentYearMonth, "yyyy-MM", null, System.Globalization.DateTimeStyles.None, out var date))
        {
            CurrentYearMonth = date.AddMonths(-1).ToString("yyyy-MM");
        }
    }

    /// <summary>
    /// 下一月
    /// </summary>
    private void NextMonth()
    {
        if (DateTime.TryParseExact(_currentYearMonth, "yyyy-MM", null, System.Globalization.DateTimeStyles.None, out var date))
        {
            CurrentYearMonth = date.AddMonths(1).ToString("yyyy-MM");
        }
    }

    /// <summary>
    /// 显示添加交易
    /// </summary>
    private void ShowAddTransaction()
    {
        SelectedTabIndex = "1"; // 切换到添加交易标签页
        AddTransactionViewModel.Reset();
    }

    /// <summary>
    /// 当前年月变更时的处理
    /// </summary>
    private async void OnCurrentYearMonthChanged()
    {
        OnPropertyChanged(nameof(FormattedCurrentYearMonth));
        await RefreshAsync();
    }

    /// <summary>
    /// 交易添加后的处理
    /// </summary>
    private async void OnTransactionAdded(object? sender, EventArgs e)
    {
        SelectedTabIndex = "0"; // 切换回交易列表标签页
        await RefreshAsync();
    }

    #endregion
}
