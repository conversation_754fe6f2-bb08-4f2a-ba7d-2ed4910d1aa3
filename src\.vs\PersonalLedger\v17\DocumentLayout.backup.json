{"Version": 1, "WorkspaceRootPath": "F:\\dotnet_workspace\\ledger\\src\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{A6981929-A766-4BB2-8D2A-0D789CEE745A}|Ledger.Db\\Ledger.Db.csproj|f:\\dotnet_workspace\\ledger\\src\\ledger.db\\models\\user.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A6981929-A766-4BB2-8D2A-0D789CEE745A}|Ledger.Db\\Ledger.Db.csproj|solutionrelative:ledger.db\\models\\user.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A6981929-A766-4BB2-8D2A-0D789CEE745A}|Ledger.Db\\Ledger.Db.csproj|f:\\dotnet_workspace\\ledger\\src\\ledger.db\\models\\ledgercontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A6981929-A766-4BB2-8D2A-0D789CEE745A}|Ledger.Db\\Ledger.Db.csproj|solutionrelative:ledger.db\\models\\ledgercontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A6981929-A766-4BB2-8D2A-0D789CEE745A}|Ledger.Db\\Ledger.Db.csproj|f:\\dotnet_workspace\\ledger\\src\\ledger.db\\models\\transaction.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A6981929-A766-4BB2-8D2A-0D789CEE745A}|Ledger.Db\\Ledger.Db.csproj|solutionrelative:ledger.db\\models\\transaction.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{757A53AE-A073-4D51-A03A-09473427E10B}|Ledger.App\\Ledger.App.csproj|f:\\dotnet_workspace\\ledger\\src\\ledger.app\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{757A53AE-A073-4D51-A03A-09473427E10B}|Ledger.App\\Ledger.App.csproj|solutionrelative:ledger.app\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "User.cs", "DocumentMoniker": "F:\\dotnet_workspace\\ledger\\src\\Ledger.Db\\Models\\User.cs", "RelativeDocumentMoniker": "Ledger.Db\\Models\\User.cs", "ToolTip": "F:\\dotnet_workspace\\ledger\\src\\Ledger.Db\\Models\\User.cs", "RelativeToolTip": "Ledger.Db\\Models\\User.cs", "ViewState": "AgIAAAsAAAAAAAAAAAAYwBYAAAAqAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-26T09:06:01.08Z", "IsPinned": true, "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "Transaction.cs", "DocumentMoniker": "F:\\dotnet_workspace\\ledger\\src\\Ledger.Db\\Models\\Transaction.cs", "RelativeDocumentMoniker": "Ledger.Db\\Models\\Transaction.cs", "ToolTip": "F:\\dotnet_workspace\\ledger\\src\\Ledger.Db\\Models\\Transaction.cs", "RelativeToolTip": "Ledger.Db\\Models\\Transaction.cs", "ViewState": "AgIAAAgAAAAAAAAAAAA0wDEAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-26T09:06:36.137Z"}, {"$type": "Document", "DocumentIndex": 1, "Title": "LedgerContext.cs", "DocumentMoniker": "F:\\dotnet_workspace\\ledger\\src\\Ledger.Db\\Models\\LedgerContext.cs", "RelativeDocumentMoniker": "Ledger.Db\\Models\\LedgerContext.cs", "ToolTip": "F:\\dotnet_workspace\\ledger\\src\\Ledger.Db\\Models\\LedgerContext.cs", "RelativeToolTip": "Ledger.Db\\Models\\LedgerContext.cs", "ViewState": "AgIAAAYAAAAAAAAAAAAAAAgAAAAaAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-26T09:05:01.679Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "appsettings.json", "DocumentMoniker": "F:\\dotnet_workspace\\ledger\\src\\Ledger.App\\appsettings.json", "RelativeDocumentMoniker": "Ledger.App\\appsettings.json", "ToolTip": "F:\\dotnet_workspace\\ledger\\src\\Ledger.App\\appsettings.json", "RelativeToolTip": "Ledger.App\\appsettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAIAAACkAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-05-26T09:03:33.838Z"}]}]}]}