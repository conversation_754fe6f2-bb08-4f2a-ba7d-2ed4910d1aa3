info: Microsoft.Hosting.Lifetime[0]
      Application started. Press Ctrl+C to shut down.
info: Microsoft.Hosting.Lifetime[0]
      Hosting environment: Production
info: Microsoft.Hosting.Lifetime[0]
      Content root path: F:\dotnet_workspace\ledger\src\Ledger.App
info: Ledger.App.ViewModels.MainViewModel[0]
      MainViewModel.InitializeAsync started - UserId: 1, YearMonth: 2025-05
info: Ledger.Services.TransactionService[0]
      Querying transactions for user 1 in 2025-05
dbug: Microsoft.EntityFrameworkCore.Database.Command[20103]
      Creating DbCommand for 'ExecuteReader'.
dbug: Microsoft.EntityFrameworkCore.Database.Command[20104]
      Created DbCommand for 'ExecuteReader' (12ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20106]
      Initialized DbCommand for 'ExecuteReader' (30ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20100]
      Executing DbCommand [Parameters=[@__type_0='?'], CommandType='Text', CommandTimeout='30']
      SELECT c.id, c.created_at, c.icon, c.is_default, c.name, c.sort_order, c.type, c.updated_at
      FROM categories AS c
      WHERE c.type = @__type_0
      ORDER BY c.sort_order, c.name
fail: Microsoft.EntityFrameworkCore.Query[10100]
      An exception occurred while iterating over the results of a query for context type 'Ledger.Db.Models.LedgerContext'.
      System.InvalidOperationException: A second operation was started on this context instance before a previous operation completed. This is usually caused by different threads concurrently using the same instance of DbContext. For more information on how to avoid threading issues with DbContext, see https://go.microsoft.com/fwlink/?linkid=2097913.
         at Microsoft.EntityFrameworkCore.Infrastructure.Internal.ConcurrencyDetector.EnterCriticalSection()
         at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
      System.InvalidOperationException: A second operation was started on this context instance before a previous operation completed. This is usually caused by different threads concurrently using the same instance of DbContext. For more information on how to avoid threading issues with DbContext, see https://go.microsoft.com/fwlink/?linkid=2097913.
         at Microsoft.EntityFrameworkCore.Infrastructure.Internal.ConcurrencyDetector.EnterCriticalSection()
         at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
fail: Ledger.Services.TransactionService[0]
      Error retrieving transactions for user 1 in 2025-05
      System.InvalidOperationException: A second operation was started on this context instance before a previous operation completed. This is usually caused by different threads concurrently using the same instance of DbContext. For more information on how to avoid threading issues with DbContext, see https://go.microsoft.com/fwlink/?linkid=2097913.
         at Microsoft.EntityFrameworkCore.Infrastructure.Internal.ConcurrencyDetector.EnterCriticalSection()
         at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
         at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
         at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
         at Ledger.Services.TransactionService.GetMonthlyTransactionsAsync(Int32 userId, String yearMonth) in F:\dotnet_workspace\ledger\src\Ledger.Services\TransactionService.cs:line 30
info: Ledger.App.ViewModels.MainViewModel[0]
      Loaded 0 transactions for 2025-05
info: Ledger.Services.TransactionService[0]
      Generating statistics for user 1 in 2025-05
fail: Microsoft.EntityFrameworkCore.Query[10100]
      An exception occurred while iterating over the results of a query for context type 'Ledger.Db.Models.LedgerContext'.
      System.InvalidOperationException: A second operation was started on this context instance before a previous operation completed. This is usually caused by different threads concurrently using the same instance of DbContext. For more information on how to avoid threading issues with DbContext, see https://go.microsoft.com/fwlink/?linkid=2097913.
         at Microsoft.EntityFrameworkCore.Infrastructure.Internal.ConcurrencyDetector.EnterCriticalSection()
         at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
      System.InvalidOperationException: A second operation was started on this context instance before a previous operation completed. This is usually caused by different threads concurrently using the same instance of DbContext. For more information on how to avoid threading issues with DbContext, see https://go.microsoft.com/fwlink/?linkid=2097913.
         at Microsoft.EntityFrameworkCore.Infrastructure.Internal.ConcurrencyDetector.EnterCriticalSection()
         at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
fail: Ledger.Services.TransactionService[0]
      Error generating statistics for user 1 in 2025-05
      System.InvalidOperationException: A second operation was started on this context instance before a previous operation completed. This is usually caused by different threads concurrently using the same instance of DbContext. For more information on how to avoid threading issues with DbContext, see https://go.microsoft.com/fwlink/?linkid=2097913.
         at Microsoft.EntityFrameworkCore.Infrastructure.Internal.ConcurrencyDetector.EnterCriticalSection()
         at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
         at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
         at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
         at Ledger.Services.TransactionService.GetMonthlyStatisticsAsync(Int32 userId, String yearMonth) in F:\dotnet_workspace\ledger\src\Ledger.Services\TransactionService.cs:line 143
info: Ledger.App.ViewModels.MainViewModel[0]
      Loaded statistics for user 1 in 2025-05
info: Ledger.App.ViewModels.MainViewModel[0]
      MainViewModel.InitializeAsync completed - Loaded 0 transactions
info: Microsoft.EntityFrameworkCore.Database.Command[20101]
      Executed DbCommand (210ms) [Parameters=[@__type_0='?'], CommandType='Text', CommandTimeout='30']
      SELECT c.id, c.created_at, c.icon, c.is_default, c.name, c.sort_order, c.type, c.updated_at
      FROM categories AS c
      WHERE c.type = @__type_0
      ORDER BY c.sort_order, c.name
dbug: Microsoft.EntityFrameworkCore.Database.Command[20301]
      Closing data reader to 'postgres' on server 'tcp://aws-0-ap-southeast-1.pooler.supabase.com:6543'.
dbug: Microsoft.EntityFrameworkCore.Database.Command[20300]
      A data reader for 'postgres' on server 'tcp://aws-0-ap-southeast-1.pooler.supabase.com:6543' is being disposed after spending 190ms reading results.
info: Ledger.Services.CategoryService[0]
      Retrieved 12 categories for type expense
info: Ledger.App.ViewModels.MainViewModel[0]
      Loaded 12 categories for type expense
