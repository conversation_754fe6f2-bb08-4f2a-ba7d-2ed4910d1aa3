info: Microsoft.Hosting.Lifetime[0]
      Application started. Press Ctrl+C to shut down.
info: Microsoft.Hosting.Lifetime[0]
      Hosting environment: Production
info: Microsoft.Hosting.Lifetime[0]
      Content root path: F:\dotnet_workspace\ledger\src\Ledger.App
info: Ledger.App.ViewModels.MainViewModel[0]
      MainViewModel.InitializeAsync started - UserId: 1, YearMonth: 2025-05
info: Ledger.Services.TransactionService[0]
      Querying transactions for user 1 in 2025-05
dbug: Microsoft.EntityFrameworkCore.Database.Command[20103]
      Creating DbCommand for 'ExecuteReader'.
dbug: Microsoft.EntityFrameworkCore.Database.Command[20104]
      Created DbCommand for 'ExecuteReader' (13ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20106]
      Initialized DbCommand for 'ExecuteReader' (23ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20100]
      Executing DbCommand [Parameters=[@__userId_0='?' (DbType = Int32), @__yearMonth_1='?'], CommandType='Text', CommandTimeout='30']
      SELECT t.id, t.amount, t.category_id, t.created_at, t.description, t.is_deleted, t.transaction_date, t.type, t.updated_at, t.user_id, t.year_month, c.id, c.created_at, c.icon, c.is_default, c.name, c.sort_order, c.type, c.updated_at
      FROM transactions AS t
      INNER JOIN categories AS c ON t.category_id = c.id
      WHERE t.user_id = @__userId_0 AND t.year_month = @__yearMonth_1 AND NOT (t.is_deleted)
      ORDER BY t.transaction_date DESC, t.created_at DESC
info: Microsoft.EntityFrameworkCore.Database.Command[20101]
      Executed DbCommand (161ms) [Parameters=[@__userId_0='?' (DbType = Int32), @__yearMonth_1='?'], CommandType='Text', CommandTimeout='30']
      SELECT t.id, t.amount, t.category_id, t.created_at, t.description, t.is_deleted, t.transaction_date, t.type, t.updated_at, t.user_id, t.year_month, c.id, c.created_at, c.icon, c.is_default, c.name, c.sort_order, c.type, c.updated_at
      FROM transactions AS t
      INNER JOIN categories AS c ON t.category_id = c.id
      WHERE t.user_id = @__userId_0 AND t.year_month = @__yearMonth_1 AND NOT (t.is_deleted)
      ORDER BY t.transaction_date DESC, t.created_at DESC
dbug: Microsoft.EntityFrameworkCore.Database.Command[20301]
      Closing data reader to 'postgres' on server 'tcp://aws-0-ap-southeast-1.pooler.supabase.com:6543'.
dbug: Microsoft.EntityFrameworkCore.Database.Command[20300]
      A data reader for 'postgres' on server 'tcp://aws-0-ap-southeast-1.pooler.supabase.com:6543' is being disposed after spending 110ms reading results.
info: Ledger.Services.TransactionService[0]
      Retrieved 19 transactions for user 1 in 2025-05
info: Ledger.App.ViewModels.MainViewModel[0]
      Loaded 19 transactions for 2025-05
info: Ledger.App.ViewModels.MainViewModel[0]
      Transactions loaded, now loading statistics...
info: Ledger.Services.TransactionService[0]
      Generating statistics for user 1 in 2025-05
dbug: Microsoft.EntityFrameworkCore.Database.Command[20103]
      Creating DbCommand for 'ExecuteReader'.
dbug: Microsoft.EntityFrameworkCore.Database.Command[20104]
      Created DbCommand for 'ExecuteReader' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20106]
      Initialized DbCommand for 'ExecuteReader' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20100]
      Executing DbCommand [Parameters=[@__userId_0='?' (DbType = Int32), @__yearMonth_1='?'], CommandType='Text', CommandTimeout='30']
      SELECT COALESCE(sum(t.amount), 0.0)
      FROM transactions AS t
      WHERE t.user_id = @__userId_0 AND t.year_month = @__yearMonth_1 AND NOT (t.is_deleted) AND t.type = 'income'
dbug: Microsoft.EntityFrameworkCore.Database.Command[20105]
      A DbCommand was canceled (30,483ms) [Parameters=[@__userId_0='?' (DbType = Int32), @__yearMonth_1='?'], CommandType='Text', CommandTimeout='30']
      SELECT COALESCE(sum(t.amount), 0.0)
      FROM transactions AS t
      WHERE t.user_id = @__userId_0 AND t.year_month = @__yearMonth_1 AND NOT (t.is_deleted) AND t.type = 'income'
warn: Ledger.Services.TransactionService[0]
      Query timeout for statistics user 1 in 2025-05
info: Ledger.App.ViewModels.MainViewModel[0]
      Loaded statistics for user 1 in 2025-05
info: Ledger.App.ViewModels.MainViewModel[0]
      Statistics loaded
info: Ledger.App.ViewModels.MainViewModel[0]
      Loading categories for AddTransactionViewModel...
dbug: Microsoft.EntityFrameworkCore.Database.Command[20103]
      Creating DbCommand for 'ExecuteReader'.
dbug: Microsoft.EntityFrameworkCore.Database.Command[20104]
      Created DbCommand for 'ExecuteReader' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20106]
      Initialized DbCommand for 'ExecuteReader' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20100]
      Executing DbCommand [Parameters=[@__type_0='?'], CommandType='Text', CommandTimeout='30']
      SELECT c.id, c.created_at, c.icon, c.is_default, c.name, c.sort_order, c.type, c.updated_at
      FROM categories AS c
      WHERE c.type = @__type_0
      ORDER BY c.sort_order, c.name
info: Microsoft.EntityFrameworkCore.Database.Command[20101]
      Executed DbCommand (106ms) [Parameters=[@__type_0='?'], CommandType='Text', CommandTimeout='30']
      SELECT c.id, c.created_at, c.icon, c.is_default, c.name, c.sort_order, c.type, c.updated_at
      FROM categories AS c
      WHERE c.type = @__type_0
      ORDER BY c.sort_order, c.name
dbug: Microsoft.EntityFrameworkCore.Database.Command[20301]
      Closing data reader to 'postgres' on server 'tcp://aws-0-ap-southeast-1.pooler.supabase.com:6543'.
dbug: Microsoft.EntityFrameworkCore.Database.Command[20300]
      A data reader for 'postgres' on server 'tcp://aws-0-ap-southeast-1.pooler.supabase.com:6543' is being disposed after spending 1ms reading results.
info: Ledger.Services.CategoryService[0]
      Retrieved 12 categories for type expense
info: Ledger.App.ViewModels.MainViewModel[0]
      Loaded 12 categories for type expense
info: Ledger.App.ViewModels.MainViewModel[0]
      Categories loaded
info: Ledger.App.ViewModels.MainViewModel[0]
      MainViewModel.InitializeAsync completed - Loaded 19 transactions
