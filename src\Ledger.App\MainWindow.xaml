<Window x:Class="Ledger.App.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:Ledger.App"
        mc:Ignorable="d"
        Title="个人记账本" Height="700" Width="1000"
        WindowStartupLocation="CenterScreen"
        Loaded="Window_Loaded">

    <Window.Resources>
        <!-- 样式定义 -->
        <Style x:Key="HeaderTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="18"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Margin" Value="0,0,0,10"/>
        </Style>

        <Style x:Key="SubHeaderTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Margin" Value="0,0,0,5"/>
        </Style>

        <Style x:Key="CardStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="15"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.2" BlurRadius="5"/>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="PrimaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#2196F3"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="4"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#1976D2"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#1565C0"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="SecondaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#F5F5F5"/>
            <Setter Property="Foreground" Value="#333"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#DDD"/>
            <Setter Property="Padding" Value="12,6"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="4"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#E0E0E0"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid Background="#F8F9FA">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- 顶部标题栏 -->
        <Border Grid.Row="0" Background="White" BorderBrush="#E0E0E0" BorderThickness="0,0,0,1" Padding="20,15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <TextBlock Text="💰" FontSize="24" Margin="0,0,10,0"/>
                    <TextBlock Text="个人记账本" Style="{StaticResource HeaderTextStyle}" VerticalAlignment="Center"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                    <Button Content="◀" Style="{StaticResource SecondaryButtonStyle}"
                            Command="{Binding PreviousMonthCommand}" Margin="0,0,5,0"/>
                    <TextBlock Text="{Binding FormattedCurrentYearMonth}"
                               FontSize="16" FontWeight="SemiBold"
                               VerticalAlignment="Center" Margin="10,0"/>
                    <Button Content="▶" Style="{StaticResource SecondaryButtonStyle}"
                            Command="{Binding NextMonthCommand}" Margin="5,0,15,0"/>
                    <Button Content="刷新" Style="{StaticResource SecondaryButtonStyle}"
                            Command="{Binding RefreshCommand}" Margin="0,0,10,0"/>
                    <Button Content="+ 添加流水" Style="{StaticResource PrimaryButtonStyle}"
                            Command="{Binding ShowAddTransactionCommand}"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- 主内容区域 -->
        <TabControl Grid.Row="1" SelectedIndex="{Binding SelectedTabIndex}"
                    Background="Transparent" BorderThickness="0" Margin="0">
            <TabControl.Resources>
                <Style TargetType="TabItem">
                    <Setter Property="Visibility" Value="Collapsed"/>
                </Style>
            </TabControl.Resources>

            <!-- 交易列表标签页 -->
            <TabItem Header="交易列表">
                <Grid Margin="20">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="2*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- 交易列表 -->
                    <Border Grid.Column="0" Style="{StaticResource CardStyle}" Margin="0,0,10,0">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Text="月流水记录" Style="{StaticResource HeaderTextStyle}"/>

                            <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                                <ItemsControl ItemsSource="{Binding Transactions}">
                                    <ItemsControl.ItemTemplate>
                                        <DataTemplate>
                                            <Border Background="White" BorderBrush="#F0F0F0" BorderThickness="0,0,0,1"
                                                    Padding="15,12" Margin="0,0,0,1">
                                                <Grid>
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="Auto"/>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="Auto"/>
                                                        <ColumnDefinition Width="Auto"/>
                                                    </Grid.ColumnDefinitions>

                                                    <TextBlock Grid.Column="0" Text="{Binding CategoryIcon}"
                                                               FontSize="20" VerticalAlignment="Center" Margin="0,0,15,0"/>

                                                    <StackPanel Grid.Column="1" VerticalAlignment="Center">
                                                        <TextBlock Text="{Binding CategoryName}" FontWeight="SemiBold" FontSize="14"/>
                                                        <TextBlock Text="{Binding Description}" FontSize="12"
                                                                   Foreground="#666" Margin="0,2,0,0"/>
                                                    </StackPanel>

                                                    <TextBlock Grid.Column="2" Text="{Binding FormattedTransactionDate}"
                                                               FontSize="12" Foreground="#666" VerticalAlignment="Center" Margin="0,0,15,0"/>

                                                    <TextBlock Grid.Column="3" Text="{Binding FormattedAmount}"
                                                               FontSize="16" FontWeight="Bold" VerticalAlignment="Center"
                                                               Foreground="{Binding AmountColor}"/>
                                                </Grid>
                                            </Border>
                                        </DataTemplate>
                                    </ItemsControl.ItemTemplate>
                                </ItemsControl>
                            </ScrollViewer>

                            <!-- 加载指示器 -->
                            <Grid Grid.Row="1">
                                <Grid.Style>
                                    <Style TargetType="Grid">
                                        <Setter Property="Visibility" Value="Collapsed"/>
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding IsLoading}" Value="True">
                                                <Setter Property="Visibility" Value="Visible"/>
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </Grid.Style>
                                <TextBlock Text="加载中..." HorizontalAlignment="Center" VerticalAlignment="Center"
                                           FontSize="14" Foreground="#666"/>
                            </Grid>

                            <!-- 空数据提示 -->
                            <Grid Grid.Row="1" Visibility="{Binding Transactions.Count, Converter={x:Static local:Converters.CountToVisibilityConverter}}">
                                <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                    <TextBlock Text="📝" FontSize="48" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                                    <TextBlock Text="本月还没有流水记录" FontSize="16" Foreground="#666" HorizontalAlignment="Center"/>
                                    <TextBlock Text="点击右上角&quot;添加流水&quot;开始记账吧" FontSize="12" Foreground="#999"
                                               HorizontalAlignment="Center" Margin="0,5,0,0"/>
                                </StackPanel>
                            </Grid>
                        </Grid>
                    </Border>

                    <!-- 月度统计 -->
                    <Border Grid.Column="1" Style="{StaticResource CardStyle}">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Text="月度统计" Style="{StaticResource HeaderTextStyle}"/>

                            <StackPanel Grid.Row="1" DataContext="{Binding MonthlyStatisticsViewModel}">
                                <!-- 收支概览 -->
                                <Border Background="#E8F5E8" CornerRadius="6" Padding="15" Margin="0,0,0,15">
                                    <Grid>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>

                                        <Grid Grid.Row="0" Margin="0,0,0,8">
                                            <TextBlock Text="总收入" FontSize="12" Foreground="#666" HorizontalAlignment="Left"/>
                                            <TextBlock Text="{Binding FormattedTotalIncome}" FontSize="14" FontWeight="Bold" Foreground="#4CAF50" HorizontalAlignment="Right"/>
                                        </Grid>

                                        <Grid Grid.Row="1" Margin="0,0,0,8">
                                            <TextBlock Text="总支出" FontSize="12" Foreground="#666" HorizontalAlignment="Left"/>
                                            <TextBlock Text="{Binding FormattedTotalExpense}" FontSize="14" FontWeight="Bold" Foreground="#F44336" HorizontalAlignment="Right"/>
                                        </Grid>

                                        <Separator Grid.Row="2" Margin="0,5,0,8"/>

                                        <Grid Grid.Row="2">
                                            <TextBlock Text="净收入" FontSize="14" FontWeight="SemiBold" HorizontalAlignment="Left"/>
                                            <TextBlock Text="{Binding FormattedNetIncome}" FontSize="16" FontWeight="Bold"
                                                       Foreground="{Binding NetIncomeColor}" HorizontalAlignment="Right"/>
                                        </Grid>
                                    </Grid>
                                </Border>

                                <!-- 交易统计 -->
                                <Border Background="#F0F8FF" CornerRadius="6" Padding="15">
                                    <Grid>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>

                                        <Grid Grid.Row="0" Margin="0,0,0,8">
                                            <TextBlock Text="总笔数" FontSize="12" Foreground="#666" HorizontalAlignment="Left"/>
                                            <TextBlock Text="{Binding TransactionCount}" FontSize="14" FontWeight="Bold" HorizontalAlignment="Right"/>
                                        </Grid>

                                        <Grid Grid.Row="1" Margin="0,0,0,8">
                                            <TextBlock Text="收入笔数" FontSize="12" Foreground="#666" HorizontalAlignment="Left"/>
                                            <TextBlock Text="{Binding IncomeCount}" FontSize="14" FontWeight="Bold" Foreground="#4CAF50" HorizontalAlignment="Right"/>
                                        </Grid>

                                        <Grid Grid.Row="2" Margin="0,0,0,8">
                                            <TextBlock Text="支出笔数" FontSize="12" Foreground="#666" HorizontalAlignment="Left"/>
                                            <TextBlock Text="{Binding ExpenseCount}" FontSize="14" FontWeight="Bold" Foreground="#F44336" HorizontalAlignment="Right"/>
                                        </Grid>

                                        <Grid Grid.Row="3">
                                            <TextBlock Text="平均支出" FontSize="12" Foreground="#666" HorizontalAlignment="Left"/>
                                            <TextBlock Text="{Binding FormattedAverageExpense}" FontSize="14" FontWeight="Bold" HorizontalAlignment="Right"/>
                                        </Grid>
                                    </Grid>
                                </Border>
                            </StackPanel>
                        </Grid>
                    </Border>
                </Grid>
            </TabItem>

            <!-- 添加交易标签页 -->
            <TabItem Header="添加交易">
                <Grid Margin="20">
                    <Border Style="{StaticResource CardStyle}" MaxWidth="700" MinWidth="600" HorizontalAlignment="Center" VerticalAlignment="Top">
                        <Grid DataContext="{Binding AddTransactionViewModel}" Margin="30,25">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <Grid Grid.Row="0">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="添加流水记录" Style="{StaticResource HeaderTextStyle}"/>
                                <Button Grid.Column="1" Content="✕" FontSize="16" Width="30" Height="30"
                                        Background="Transparent" BorderThickness="0" Foreground="#666"
                                        Command="{Binding CloseCommand}" VerticalAlignment="Top"
                                        ToolTip="关闭"/>
                            </Grid>

                            <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Margin="0,15,0,25">
                                <StackPanel>
                                    <!-- 交易类型选择 -->
                                    <TextBlock Text="交易类型" Style="{StaticResource SubHeaderTextStyle}"/>
                                    <StackPanel Orientation="Horizontal" Margin="0,5,0,20">
                                        <RadioButton Content="支出" IsChecked="{Binding IsExpense}"
                                                     FontSize="15" Margin="0,0,30,0"/>
                                        <RadioButton Content="收入" IsChecked="{Binding IsIncome}"
                                                     FontSize="15"/>
                                    </StackPanel>

                                    <!-- 金额输入 -->
                                    <TextBlock Text="金额" Style="{StaticResource SubHeaderTextStyle}"/>
                                    <TextBox Text="{Binding Amount, StringFormat=F2}" FontSize="16" Padding="12,10"
                                             Margin="0,5,0,20" BorderBrush="#DDD" Height="45"/>

                                    <!-- 分类选择 -->
                                    <TextBlock Text="分类" Style="{StaticResource SubHeaderTextStyle}"/>
                                    <ComboBox ItemsSource="{Binding Categories}"
                                              SelectedItem="{Binding SelectedCategory}"
                                              FontSize="15" Padding="12,10" Margin="0,5,0,20" BorderBrush="#DDD" Height="45">
                                        <ComboBox.ItemTemplate>
                                            <DataTemplate>
                                                <StackPanel Orientation="Horizontal">
                                                    <TextBlock Text="{Binding Icon}" FontSize="16" Margin="0,0,10,0" VerticalAlignment="Center"/>
                                                    <TextBlock Text="{Binding Name}" FontSize="15" VerticalAlignment="Center"/>
                                                </StackPanel>
                                            </DataTemplate>
                                        </ComboBox.ItemTemplate>
                                    </ComboBox>

                                    <!-- 日期选择 -->
                                    <TextBlock Text="日期" Style="{StaticResource SubHeaderTextStyle}"/>
                                    <DatePicker SelectedDate="{Binding TransactionDate, Converter={x:Static local:Converters.DateOnlyToDateTimeConverter}}"
                                                FontSize="15" Padding="12,10" Margin="0,5,0,20" BorderBrush="#DDD" Height="45"/>

                                    <!-- 描述输入 -->
                                    <TextBlock Text="描述（可选）" Style="{StaticResource SubHeaderTextStyle}"/>
                                    <TextBox Text="{Binding Description}" FontSize="15" Padding="12,10"
                                             Margin="0,5,0,20" BorderBrush="#DDD" Height="80"
                                             TextWrapping="Wrap" AcceptsReturn="True" VerticalScrollBarVisibility="Auto"/>
                                </StackPanel>
                            </ScrollViewer>

                            <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,10,0,0">
                                <Button Content="重置" Style="{StaticResource SecondaryButtonStyle}"
                                        Command="{Binding ResetCommand}" Margin="0,0,15,0" Padding="20,10"/>
                                <Button Content="保存" Style="{StaticResource PrimaryButtonStyle}"
                                        Command="{Binding SaveTransactionCommand}" Padding="20,10"/>
                            </StackPanel>
                        </Grid>
                    </Border>
                </Grid>
            </TabItem>
        </TabControl>
    </Grid>
</Window>
